<script setup>
import { User, Lock } from '@element-plus/icons-vue'
import { messageConfig } from 'element-plus'
import { ref } from 'vue'
//控制注册与登录表单的显示， 默认显示注册
const isRegister = ref(false)
const registerDate=ref({
    username:'',
    passwored:'',
    repasswored:''
    }
)
//校验密码
const checkpassword=(rule,vales,callback)=>{
    if(vales===''){
        callback(new Error('请再次确认密码'))
    }else if(vales!==registerDate.value.passwored){
        callback(new Error('输入密码不一致'))
    }else{
        callback()
    }
}
const rules={
    username:[
        {required:true,messageConfig:'请输入用户名',trigger:'blur'},
        {min:5,max:16,messageConfig:'长度为5—16位非空字符',trigger:'blur'}
    ],
    passwored:[
        {required:true,messageConfig:'请输入密码1',trigger:'blur'},
        {min:5,max:16,messageConfig:'长度为5—16位非空字符',trigger:'blur'}
    ],
    repasswored:[
        {validator:checkpassword,trigger:'blur'}
    ]
}
</script>

<template>
    <el-row class="login-page">
        <el-col :span="12" class="bg"></el-col>
        <el-col :span="6" :offset="3" class="form">
            <!-- 注册表单 -->
            <el-form ref="form" size="large" autocomplete="off" v-if="isRegister":model="registerDate" :rule="rules">
                <el-form-item>
                    <h1>注册</h1>
                </el-form-item>
                <el-form-item prpo="username">
                    <el-input :prefix-icon="User" placeholder="请输入用户名" v-model="registerDate.name"></el-input>
                </el-form-item>
                <el-form-item prpo="password">
                    <el-input :prefix-icon="Lock" type="password" placeholder="请输入密码"v-model="registerDate.passwored"></el-input>
                </el-form-item>
                <el-form-item prpo="repassword">
                    <el-input :prefix-icon="Lock" type="password" placeholder="请输入再次密码"v-model="registerDate.repasswored"></el-input>
                </el-form-item>
                <!-- 注册按钮 -->
                <el-form-item>
                    <el-button class="button" type="primary" auto-insert-space>
                        注册
                    </el-button>
                </el-form-item>
                <el-form-item class="flex">
                    <el-link type="info" :underline="false" @click="isRegister = false">
                        ← 返回
                    </el-link>
                </el-form-item>
            </el-form>
            <!-- 登录表单 -->
            <el-form ref="form" size="large" autocomplete="off" v-else>
                <el-form-item>
                    <h1>登录</h1>
                </el-form-item>
                <el-form-item>
                    <el-input :prefix-icon="User" placeholder="请输入用户名"></el-input>
                </el-form-item>
                <el-form-item>
                    <el-input name="password" :prefix-icon="Lock" type="password" placeholder="请输入密码"></el-input>
                </el-form-item>
                <el-form-item class="flex">
                    <div class="flex">
                        <el-checkbox>记住我</el-checkbox>
                        <el-link type="primary" :underline="false">忘记密码？</el-link>
                    </div>
                </el-form-item>
                <!-- 登录按钮 -->
                <el-form-item>
                    <el-button class="button" type="primary" auto-insert-space>登录</el-button>
                </el-form-item>
                <el-form-item class="flex">
                    <el-link type="info" :underline="false" @click="isRegister = true">
                        注册 →
                    </el-link>
                </el-form-item>
            </el-form>
        </el-col>
    </el-row>
</template>

<style lang="scss" scoped>
/* 样式 */
.login-page {
    height: 100vh;
    background-color: #fff;

    .bg {
        background: url('@/assets/logo2.png') no-repeat 60% center / 240px auto,
            url('@/assets/login_bg.jpg') no-repeat center / cover;
        border-radius: 0 20px 20px 0;
    }

    .form {
        display: flex;
        flex-direction: column;
        justify-content: center;
        user-select: none;

        .title {
            margin: 0 auto;
        }

        .button {
            width: 100%;
        }

        .flex {
            width: 100%;
            display: flex;
            justify-content: space-between;
        }
    }
}
</style>